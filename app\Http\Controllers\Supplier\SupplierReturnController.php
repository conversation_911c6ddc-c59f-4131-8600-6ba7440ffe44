<?php

namespace App\Http\Controllers\Supplier;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ReturnModel;
use App\Models\SupplierDelivery;
use App\Models\Supplier;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Traits\SupplierHelper;

class SupplierReturnController extends Controller
{
    use SupplierHelper;
    /**
     * Display a listing of returns and cancelled deliveries.
     */
    public function index(Request $request)
    {
        // Get current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();

        // Get current month for default filtering
        $currentMonth = Carbon::now()->format('Y-m');
        $filterMonth = $request->get('month', $currentMonth);

        // Parse the filter month
        $startDate = Carbon::createFromFormat('Y-m', $filterMonth)->startOfMonth();
        $endDate = Carbon::createFromFormat('Y-m', $filterMonth)->endOfMonth();

        // Get regular returns (only show unresolved returns - not completed)
        $returnsQuery = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy']);
        $returnsQuery->whereBetween('return_date', [$startDate, $endDate]);
        $returnsQuery->where('supplier_id', $supplier->id);
        $returnsQuery->where('status', '!=', 'completed'); // Only show returns that haven't been resolved

        // Get cancelled deliveries for this supplier
        $cancelledDeliveriesQuery = SupplierDelivery::with(['product', 'supplier', 'receivedBy']);
        $cancelledDeliveriesQuery->whereBetween('delivery_date', [$startDate, $endDate]);
        $cancelledDeliveriesQuery->where('supplier_id', $supplier->id);
        $cancelledDeliveriesQuery->where('status', 'cancelled');
        
        // Apply search filter if provided
        if ($request->filled('search')) {
            $searchTerm = $request->get('search');
            $returnsQuery->where(function ($q) use ($searchTerm) {
                $q->whereHas('product', function ($productQuery) use ($searchTerm) {
                    $productQuery->where('name', 'like', "%{$searchTerm}%");
                })
                ->orWhereHas('store', function ($storeQuery) use ($searchTerm) {
                    $storeQuery->where('name', 'like', "%{$searchTerm}%");
                });
            });

            $cancelledDeliveriesQuery->where(function ($q) use ($searchTerm) {
                $q->whereHas('product', function ($productQuery) use ($searchTerm) {
                    $productQuery->where('name', 'like', "%{$searchTerm}%");
                });
            });
        }

        // Apply status filter if provided
        if ($request->filled('status')) {
            $returnsQuery->where('status', $request->get('status'));
        }

        // Apply reason filter if provided
        if ($request->filled('reason')) {
            $returnsQuery->where('reason', $request->get('reason'));
        }

        // Sort by newest first (descending order by date)
        $returns = $returnsQuery->orderBy('return_date', 'desc')->paginate(15);
        $cancelledDeliveries = $cancelledDeliveriesQuery->orderBy('delivery_date', 'desc')->paginate(15);

        // Get statistics
        $stats = [
            'total_returns' => ReturnModel::whereBetween('return_date', [$startDate, $endDate])
                ->where('supplier_id', $supplier->id)->count(),
            'requested_returns' => ReturnModel::whereBetween('return_date', [$startDate, $endDate])
                ->where('supplier_id', $supplier->id)->where('status', 'requested')->count(),
            'approved_returns' => ReturnModel::whereBetween('return_date', [$startDate, $endDate])
                ->where('supplier_id', $supplier->id)->where('status', 'approved')->count(),
            'completed_returns' => ReturnModel::whereBetween('return_date', [$startDate, $endDate])
                ->where('supplier_id', $supplier->id)->where('status', 'completed')->count(),
            'cancelled_deliveries' => SupplierDelivery::whereBetween('delivery_date', [$startDate, $endDate])
                ->where('supplier_id', $supplier->id)->where('status', 'cancelled')->count(),
        ];

        return view('supplier.returns.index', compact('returns', 'cancelledDeliveries', 'stats', 'filterMonth'));
    }
    
    /**
     * Display the specified return.
     */
    public function show(ReturnModel $return)
    {
        $return->load(['product', 'store', 'supplier', 'requestedBy', 'approvedBy']);
        
        return view('supplier.returns.show', compact('return'));
    }
    
    /**
     * Respond to a return request.
     */
    public function respond(Request $request, ReturnModel $return)
    {
        // Only allow response if return is approved and involves a supplier
        if ($return->status !== 'approved' || is_null($return->supplier_id)) {
            return redirect()->route('supplier.returns.index')
                ->with('error', 'Return ini tidak dapat direspons');
        }
        
        $validatedData = $request->validate([
            'action' => 'required|in:accept,reject',
            'supplier_notes' => 'nullable|string|max:1000',
        ], [
            'action.required' => 'Aksi wajib dipilih',
            'action.in' => 'Aksi tidak valid',
            'supplier_notes.max' => 'Catatan maksimal 1000 karakter',
        ]);
        
        if ($validatedData['action'] === 'accept') {
            $return->update([
                'status' => 'in_transit',
                'admin_notes' => ($return->admin_notes ?? '') . "\n\nSupplier Response: " . ($validatedData['supplier_notes'] ?? 'Diterima'),
            ]);
            
            $message = 'Return berhasil diterima dan sedang dalam perjalanan';
        } else {
            $return->update([
                'status' => 'rejected',
                'admin_notes' => ($return->admin_notes ?? '') . "\n\nSupplier Response: " . ($validatedData['supplier_notes'] ?? 'Ditolak'),
            ]);
            
            $message = 'Return berhasil ditolak';
        }
        
        return redirect()->route('supplier.returns.index')
            ->with('success', $message);
    }

    /**
     * Delete a cancelled delivery.
     */
    public function deleteCancelledDelivery(Request $request, SupplierDelivery $delivery)
    {
        // Get current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();

        // Verify this delivery belongs to the current supplier and is cancelled
        if (!$supplier || $delivery->supplier_id !== $supplier->id || $delivery->status !== 'cancelled') {
            return redirect()->route('supplier.returns.index')
                ->with('error', 'Pengiriman tidak dapat dihapus');
        }

        $productName = $delivery->product->name;

        DB::transaction(function () use ($delivery, $supplier) {
            // Auto-approve any related warehouse returns for the same product from this supplier
            $this->autoApproveRelatedWarehouseReturns($delivery->product_id, $supplier->id, 'Supplier menghapus pengiriman yang dibatalkan');

            $delivery->delete();
        });

        return redirect()->route('supplier.returns.index')
            ->with('success', "Pengiriman yang dibatalkan untuk produk '{$productName}' berhasil dihapus");
    }

    /**
     * Resend a cancelled delivery (create new delivery based on cancelled one).
     */
    public function resendCancelledDelivery(Request $request, SupplierDelivery $delivery)
    {
        // Get current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();

        // Verify this delivery belongs to the current supplier and is cancelled
        if (!$supplier || $delivery->supplier_id !== $supplier->id || $delivery->status !== 'cancelled') {
            return redirect()->route('supplier.returns.index')
                ->with('error', 'Pengiriman tidak dapat dikirim ulang');
        }

        $validatedData = $request->validate([
            'delivery_date' => 'required|date|after_or_equal:today',
            'notes' => 'nullable|string|max:1000',
        ], [
            'delivery_date.required' => 'Tanggal pengiriman wajib diisi',
            'delivery_date.date' => 'Format tanggal tidak valid',
            'delivery_date.after_or_equal' => 'Tanggal pengiriman tidak boleh kurang dari hari ini',
            'notes.max' => 'Catatan maksimal 1000 karakter',
        ]);

        $productName = $delivery->product->name;

        DB::transaction(function () use ($delivery, $validatedData, $supplier) {
            // Create new delivery based on the cancelled one
            SupplierDelivery::create([
                'supplier_id' => $supplier->id,
                'product_id' => $delivery->product_id,
                'quantity' => $delivery->quantity,
                'unit_price' => $delivery->unit_price,
                'total_price' => $delivery->total_price,
                'delivery_date' => $validatedData['delivery_date'],
                'status' => 'pending',
                'notes' => $validatedData['notes'] ?? 'Pengiriman ulang dari pengiriman yang dibatalkan',
            ]);

            // Auto-approve any related warehouse returns for the same product from this supplier
            $this->autoApproveRelatedWarehouseReturns($delivery->product_id, $supplier->id, 'Supplier mengirim ulang produk');

            // Delete the original cancelled delivery after creating the replacement
            $delivery->delete();
        });

        return redirect()->route('supplier.returns.index')
            ->with('success', "Pengiriman ulang untuk produk '{$productName}' berhasil dibuat");
    }

    /**
     * Delete a return request permanently (accept and delete forever).
     */
    public function destroy(Request $request, ReturnModel $return)
    {
        // Get current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();

        // Verify this return belongs to the current supplier
        if (!$supplier || $return->supplier_id !== $supplier->id) {
            return redirect()->route('supplier.returns.index')
                ->with('error', 'Retur tidak dapat dihapus');
        }

        $productName = $return->product->name;

        DB::transaction(function () use ($return, $supplier) {
            // Mark as completed before deletion to indicate supplier accepted the return
            $return->update([
                'status' => 'completed',
                'completed_date' => now(),
            ]);

            // Auto-approve any related warehouse returns for the same product from this supplier
            $this->autoApproveRelatedWarehouseReturns($return->product_id, $supplier->id, 'Supplier menerima retur produk');

            // Then delete the return record
            $return->delete();
        });

        return redirect()->route('supplier.returns.index')
            ->with('success', "Produk '{$productName}' telah diterima dan dihapus permanen dari sistem");
    }

    /**
     * Send replacement product (create new delivery based on return).
     */
    public function resendReturn(Request $request, ReturnModel $return)
    {
        // Get current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();

        // Verify this return belongs to the current supplier
        if (!$supplier || $return->supplier_id !== $supplier->id) {
            return redirect()->route('supplier.returns.index')
                ->with('error', 'Produk pengganti tidak dapat dikirim');
        }

        $validatedData = $request->validate([
            'delivery_date' => 'required|date|after_or_equal:today',
            'notes' => 'nullable|string|max:1000',
        ], [
            'delivery_date.required' => 'Tanggal pengiriman wajib diisi',
            'delivery_date.date' => 'Format tanggal tidak valid',
            'delivery_date.after_or_equal' => 'Tanggal pengiriman tidak boleh kurang dari hari ini',
            'notes.max' => 'Catatan maksimal 1000 karakter',
        ]);

        DB::transaction(function () use ($return, $validatedData, $supplier) {
            // Find the most recent delivery price for this product from this supplier
            $recentDelivery = \App\Models\SupplierDelivery::where('supplier_id', $supplier->id)
                ->where('product_id', $return->product_id)
                ->whereNotNull('unit_price')
                ->orderBy('created_at', 'desc')
                ->first();

            // Use the most recent delivery price, or fallback to product price, or 0
            $unitPrice = $recentDelivery ? $recentDelivery->unit_price : ($return->product->price ?? 0);
            $totalPrice = $unitPrice * $return->quantity;

            // Create new delivery as replacement for the returned product
            \App\Models\SupplierDelivery::create([
                'supplier_id' => $supplier->id,
                'product_id' => $return->product_id,
                'quantity' => $return->quantity,
                'unit_price' => $unitPrice,
                'total_price' => $totalPrice,
                'delivery_date' => $validatedData['delivery_date'],
                'status' => 'pending',
                'notes' => $validatedData['notes'] ?? "Produk pengganti untuk retur: {$return->reason_in_indonesian}",
                'created_by' => auth()->id(),
            ]);

            // Auto-approve any related warehouse returns for the same product from this supplier
            $this->autoApproveRelatedWarehouseReturns($return->product_id, $supplier->id, 'Supplier mengirim produk pengganti');

            // Mark the return as completed since we're sending replacement
            $return->update([
                'status' => 'completed',
                'completed_date' => now(),
            ]);

            // Delete the return record since it's now resolved
            $return->delete();
        });

        return redirect()->route('supplier.returns.index')
            ->with('success', "Produk pengganti untuk '{$return->product->name}' berhasil dijadwalkan untuk dikirim");
    }

    /**
     * Auto-approve related warehouse returns when supplier takes action
     */
    private function autoApproveRelatedWarehouseReturns($productId, $supplierId, $reason)
    {
        // Find pending warehouse returns for the same product and supplier
        $warehouseReturns = ReturnModel::where('product_id', $productId)
            ->where('supplier_id', $supplierId)
            ->where('store_id', null) // Warehouse returns only
            ->where('status', 'requested')
            ->get();

        foreach ($warehouseReturns as $warehouseReturn) {
            $warehouseReturn->update([
                'status' => 'approved',
                'approved_date' => now(),
                'approved_by' => auth()->id(),
                'admin_notes' => $reason . ' - Otomatis disetujui karena supplier mengambil tindakan',
            ]);
        }

        if ($warehouseReturns->count() > 0) {
            \Log::info("Auto-approved {$warehouseReturns->count()} warehouse returns for product {$productId} from supplier {$supplierId}. Reason: {$reason}");
        }
    }
}
